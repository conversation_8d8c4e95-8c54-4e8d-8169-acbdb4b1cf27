import BaseVideoAdapter from "../videoAdapters";

/**
 * Netflix 字幕适配器
 */
class NetflixAdapter extends BaseVideoAdapter {
  getName() {
    return "netflix";
  }

  matches() {
    return /netflix\.com/.test(window.location.host);
  }

  getContainerSelector() {
    // Netflix字幕容器可能有多种
    return ".player-timedtext, .watch-video";
  }

  getSubtitleSelector() {
    return ".player-timedtext span, .timedtext span";
  }

  /**
   * Netflix容器查找
   */
  getContainer() {
    // Netflix字幕容器可能动态变化，需要更灵活的查找
    const containers = [
      ".player-timedtext",
      ".watch-video",
      ".NFPlayer",
      "#appMountPoint"
    ];

    for (const selector of containers) {
      const container = document.querySelector(selector);
      if (container) return container;
    }

    return document.body;
  }

  /**
   * Netflix字幕元素查找
   */
  findSubtitleElements(node, subtitleElements) {
    // Netflix字幕结构比较复杂，需要多种选择器
    const selectors = [
      ".player-timedtext span",
      ".timedtext span",
      "[data-uia='player-timedtext'] span"
    ];

    for (const selector of selectors) {
      try {
        // 检查节点本身
        if (node.matches && node.matches(selector)) {
          subtitleElements.add(node);
        }

        // 查找子元素
        node.querySelectorAll?.(selector).forEach((el) => {
          subtitleElements.add(el);
        });
      } catch (err) {
        // 忽略选择器错误
      }
    }
  }

  /**
   * Netflix字幕文本提取
   */
  extractText(element) {
    // Netflix字幕可能包含HTML标签，需要清理
    let text = element.textContent?.trim() || "";
    
    // 移除可能的时间戳或其他标记
    text = text.replace(/^\d{2}:\d{2}:\d{2}[,.]\d{3}\s*-->\s*\d{2}:\d{2}:\d{2}[,.]\d{3}/, "");
    text = text.replace(/^\d+\s*$/, ""); // 移除纯数字行号
    
    return text.trim();
  }

  /**
   * Netflix特殊的翻译插入逻辑
   */
  insertTranslation(originalElement, translationElement) {
    // Netflix字幕通常在特定的容器中
    const timedTextContainer = originalElement.closest(".player-timedtext") ||
                              originalElement.closest(".timedtext");
    
    if (timedTextContainer) {
      // 移除已存在的翻译
      const existingTranslation = timedTextContainer.querySelector(".kiss-subtitle-translation");
      if (existingTranslation) {
        existingTranslation.remove();
      }

      // 在字幕容器中添加翻译
      if (this.styleConfig.position === "above") {
        timedTextContainer.insertBefore(translationElement, timedTextContainer.firstChild);
      } else {
        timedTextContainer.appendChild(translationElement);
      }
    } else {
      // 回退到父元素
      const parent = originalElement.parentElement;
      if (parent) {
        const existingTranslation = parent.querySelector(".kiss-subtitle-translation");
        if (existingTranslation) {
          existingTranslation.remove();
        }
        parent.appendChild(translationElement);
      }
    }
  }

  /**
   * Netflix特殊样式
   */
  buildTranslationStyle() {
    const baseStyle = super.buildTranslationStyle();
    
    // Netflix特殊样式调整
    return `${baseStyle}
      background: rgba(0, 0, 0, 0.75);
      padding: 4px 12px;
      border-radius: 4px;
      font-family: Netflix Sans, Helvetica Neue, Segoe UI, Roboto, Ubuntu, sans-serif;
      font-weight: 400;
      text-align: center;
      max-width: 80%;
      margin: 0 auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    `;
  }

  /**
   * Netflix特殊的变化处理
   */
  handleMutations(mutations) {
    const subtitleElements = new Set();
    let hasRelevantChanges = false;

    for (const mutation of mutations) {
      if (mutation.type === "childList") {
        // Netflix页面变化很频繁，只关注字幕相关的变化
        const isSubtitleRelated = Array.from(mutation.addedNodes).some(node => 
          node.nodeType === Node.ELEMENT_NODE && 
          (node.classList?.contains("player-timedtext") ||
           node.classList?.contains("timedtext") ||
           node.querySelector?.(".player-timedtext, .timedtext"))
        );

        if (isSubtitleRelated) {
          hasRelevantChanges = true;
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.findSubtitleElements(node, subtitleElements);
            }
          });
        }
      }
    }

    if (hasRelevantChanges) {
      // Netflix字幕更新也需要防抖
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        subtitleElements.forEach((element) => {
          if (!this.processedElements.has(element)) {
            this.processSubtitleElement(element);
          }
        });
      }, 150); // Netflix稍微长一点的防抖时间
    }
  }

  /**
   * 停止时清理定时器
   */
  stop() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    super.stop();
  }
}

export default NetflixAdapter;
