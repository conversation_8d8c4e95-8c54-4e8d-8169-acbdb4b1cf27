import BaseVideoAdapter from "../videoAdapters";

/**
 * YouTube 字幕适配器
 */
class YouTubeAdapter extends BaseVideoAdapter {
  getName() {
    return "youtube";
  }

  matches() {
    return /youtube\.com/.test(window.location.host);
  }

  getContainerSelector() {
    return "#ytp-caption-window-container";
  }

  getSubtitleSelector() {
    return ".ytp-caption-segment";
  }

  /**
   * YouTube特殊处理：字幕可能在多个容器中
   */
  getContainer() {
    // 优先使用主字幕容器
    let container = document.querySelector(this.getContainerSelector());
    
    // 如果主容器不存在，尝试其他可能的容器
    if (!container) {
      container = document.querySelector(".ytp-caption-window-rollup") ||
                  document.querySelector(".caption-window") ||
                  document.querySelector("#movie_player");
    }
    
    return container || document.body;
  }

  /**
   * YouTube字幕文本提取
   */
  extractText(element) {
    // YouTube字幕可能包含多个span，需要合并文本
    const text = Array.from(element.childNodes)
      .map(node => node.textContent || "")
      .join("")
      .trim();
    
    return text;
  }

  /**
   * YouTube特殊的翻译插入逻辑
   */
  insertTranslation(originalElement, translationElement) {
    const parent = originalElement.parentElement;
    if (!parent) return;

    // 移除已存在的翻译
    const existingTranslation = parent.querySelector(".kiss-subtitle-translation");
    if (existingTranslation) {
      existingTranslation.remove();
    }

    // YouTube字幕通常在caption-window内，需要特殊处理位置
    const captionWindow = originalElement.closest(".ytp-caption-window-rollup") ||
                         originalElement.closest(".caption-window");
    
    if (captionWindow) {
      // 在字幕窗口内添加翻译
      if (this.styleConfig.position === "above") {
        captionWindow.insertBefore(translationElement, captionWindow.firstChild);
      } else {
        captionWindow.appendChild(translationElement);
      }
    } else {
      // 回退到默认行为
      super.insertTranslation(originalElement, translationElement);
    }
  }

  /**
   * YouTube特殊样式
   */
  buildTranslationStyle() {
    const baseStyle = super.buildTranslationStyle();
    
    // YouTube特殊样式调整
    return `${baseStyle}
      background: rgba(0, 0, 0, 0.8);
      padding: 2px 8px;
      border-radius: 2px;
      font-family: "YouTube Noto", Roboto, Arial, Helvetica, sans-serif;
      font-weight: 400;
      white-space: pre-wrap;
      word-wrap: break-word;
    `;
  }

  /**
   * 处理YouTube的动态字幕更新
   */
  handleMutations(mutations) {
    // YouTube字幕更新频繁，需要去重处理
    const subtitleElements = new Set();
    let hasSubtitleChanges = false;

    for (const mutation of mutations) {
      if (mutation.type === "childList") {
        // 检查是否有字幕相关的变化
        const hasSubtitleNodes = Array.from(mutation.addedNodes).some(node => 
          node.nodeType === Node.ELEMENT_NODE && 
          (node.matches?.(this.getSubtitleSelector()) || 
           node.querySelector?.(this.getSubtitleSelector()))
        );

        if (hasSubtitleNodes) {
          hasSubtitleChanges = true;
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.findSubtitleElements(node, subtitleElements);
            }
          });
        }
      }
    }

    // 只有当确实有字幕变化时才处理
    if (hasSubtitleChanges) {
      // 使用防抖处理，避免过于频繁的翻译请求
      clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        subtitleElements.forEach((element) => {
          if (!this.processedElements.has(element)) {
            this.processSubtitleElement(element);
          }
        });
      }, 100);
    }
  }

  /**
   * 停止时清理定时器
   */
  stop() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    super.stop();
  }
}

export default YouTubeAdapter;
