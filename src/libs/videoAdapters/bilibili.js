import BaseVideoAdapter from "../videoAdapters";

/**
 * Bilibili 字幕适配器
 */
class BilibiliAdapter extends BaseVideoAdapter {
  getName() {
    return "bilibili";
  }

  matches() {
    return /bilibili\.com/.test(window.location.host);
  }

  getContainerSelector() {
    return ".bpx-player-subtitle-panel, .bilibili-player-video-subtitle";
  }

  getSubtitleSelector() {
    return ".bpx-player-subtitle-panel-text, .bilibili-player-video-subtitle-text";
  }

  /**
   * Bilibili容器查找
   */
  getContainer() {
    const containers = [
      ".bpx-player-subtitle-panel",
      ".bilibili-player-video-subtitle",
      ".bpx-player-container",
      ".bilibili-player-video"
    ];

    for (const selector of containers) {
      const container = document.querySelector(selector);
      if (container) return container;
    }

    return document.body;
  }

  /**
   * Bilibili字幕样式
   */
  buildTranslationStyle() {
    const baseStyle = super.buildTranslationStyle();
    
    return `${baseStyle}
      background: rgba(0, 0, 0, 0.7);
      padding: 2px 8px;
      border-radius: 3px;
      font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei, sans-serif;
      font-size: 16px;
      text-align: center;
      white-space: pre-wrap;
    `;
  }
}

/**
 * Udemy 字幕适配器
 */
class UdemyAdapter extends BaseVideoAdapter {
  getName() {
    return "udemy";
  }

  matches() {
    return /udemy\.com/.test(window.location.host);
  }

  getContainerSelector() {
    return ".captions-display--captions-container, .video-viewer--container";
  }

  getSubtitleSelector() {
    return ".captions-display--captions-text, .caption-line";
  }

  buildTranslationStyle() {
    const baseStyle = super.buildTranslationStyle();
    
    return `${baseStyle}
      background: rgba(0, 0, 0, 0.8);
      padding: 4px 12px;
      border-radius: 4px;
      font-family: sf pro text, -apple-system, BlinkMacSystemFont, Roboto, segoe ui, Helvetica, Arial, sans-serif;
      text-align: center;
      white-space: pre-wrap;
    `;
  }
}

/**
 * Coursera 字幕适配器
 */
class CourseraAdapter extends BaseVideoAdapter {
  getName() {
    return "coursera";
  }

  matches() {
    return /coursera\.org/.test(window.location.host);
  }

  getContainerSelector() {
    return ".vjs-text-track-display, .video-container";
  }

  getSubtitleSelector() {
    return ".vjs-text-track-cue, .subtitle-text";
  }

  buildTranslationStyle() {
    const baseStyle = super.buildTranslationStyle();
    
    return `${baseStyle}
      background: rgba(0, 0, 0, 0.75);
      padding: 3px 10px;
      border-radius: 3px;
      font-family: OpenSans, Arial, sans-serif;
      text-align: center;
      white-space: pre-wrap;
    `;
  }
}

/**
 * Vimeo 字幕适配器
 */
class VimeoAdapter extends BaseVideoAdapter {
  getName() {
    return "vimeo";
  }

  matches() {
    return /vimeo\.com/.test(window.location.host);
  }

  getContainerSelector() {
    return ".vp-captions, .player";
  }

  getSubtitleSelector() {
    return ".vp-captions-line, .captions-text";
  }

  buildTranslationStyle() {
    const baseStyle = super.buildTranslationStyle();
    
    return `${baseStyle}
      background: rgba(0, 0, 0, 0.8);
      padding: 4px 8px;
      border-radius: 2px;
      font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
      text-align: center;
      white-space: pre-wrap;
    `;
  }
}

/**
 * Twitch 字幕适配器
 */
class TwitchAdapter extends BaseVideoAdapter {
  getName() {
    return "twitch";
  }

  matches() {
    return /twitch\.tv/.test(window.location.host);
  }

  getContainerSelector() {
    return ".video-player__overlay, .player-overlay-background";
  }

  getSubtitleSelector() {
    return ".player-captions-container span, .captions-text";
  }

  buildTranslationStyle() {
    const baseStyle = super.buildTranslationStyle();
    
    return `${baseStyle}
      background: rgba(0, 0, 0, 0.8);
      padding: 2px 6px;
      border-radius: 2px;
      font-family: "Inter", "Roobert", "Helvetica Neue", Helvetica, Arial, sans-serif;
      text-align: center;
      white-space: pre-wrap;
    `;
  }
}

export { BilibiliAdapter, UdemyAdapter, CourseraAdapter, VimeoAdapter, TwitchAdapter };
