import { kissLog } from "./log";

/**
 * 初始化 YouTube 双语字幕
 * 基于字幕容器 #ytp-caption-window-container 下的 .ytp-caption-segment
 */
export function initYouTubeBilingual(translator) {
  try {
    const container = document.querySelector("#ytp-caption-window-container");
    if (!container) return;

    const cache = new Map(); // text -> translated

    const renderBilingual = async (seg) => {
      if (!seg || seg.dataset.kissTranslated) return;
      const text = seg.textContent?.trim();
      if (!text) return;

      // 读取缓存或翻译
      let tr = cache.get(text);
      if (!tr) {
        try {
          tr = await translator.translateText(text);
          cache.set(text, tr || "");
        } catch (err) {
          kissLog(err, "yt subtitle translate");
          tr = "";
        }
      }

      // 注入译文行
      if (tr) {
        const line = document.createElement("div");
        line.textContent = tr;
        line.style.cssText = `
          font-size: 0.9em;
          opacity: 0.9;
          line-height: 1.2;
          text-shadow: 0 0 2px rgba(0,0,0,0.8);
        `;
        // 将译文添加到段落父级，以便与原文同行显示（尽量靠近）
        seg.parentElement?.appendChild(line);
        seg.dataset.kissTranslated = "1";
      }
    };

    const mo = new MutationObserver((mutations) => {
      for (const m of mutations) {
        if (m.type === "childList" && m.addedNodes?.length) {
          m.addedNodes.forEach((node) => {
            if (node.nodeType === 1) {
              if (node.classList?.contains("ytp-caption-segment")) {
                renderBilingual(node);
              }
              node
                .querySelectorAll?.(".ytp-caption-segment")
                .forEach((el) => renderBilingual(el));
            }
          });
        }
      }
    });

    mo.observe(container, { childList: true, subtree: true });
  } catch (err) {
    kissLog(err, "initYouTubeBilingual");
  }
}

/**
 * 初始化 Netflix 双语字幕
 * 观察 .player-timedtext（字幕容器）中的 span 文本
 */
export function initNetflixBilingual(translator) {
  try {
    const root = document.body;
    if (!root) return;

    const cache = new Map();

    const translateNode = async (span) => {
      if (!span || span.dataset.kissTranslated) return;
      const text = span.textContent?.trim();
      if (!text) return;
      let tr = cache.get(text);
      if (!tr) {
        try {
          tr = await translator.translateText(text);
          cache.set(text, tr || "");
        } catch (err) {
          kissLog(err, "netflix subtitle translate");
          tr = "";
        }
      }
      if (tr) {
        const line = document.createElement("div");
        line.textContent = tr;
        line.style.cssText = `font-size: 0.9em; opacity: .95; text-align: center; text-shadow: 0 0 2px rgba(0,0,0,.8);`;
        span.parentElement?.appendChild(line);
        span.dataset.kissTranslated = "1";
      }
    };

    const mo = new MutationObserver((mutations) => {
      for (const m of mutations) {
        m.addedNodes?.forEach((node) => {
          if (node.nodeType !== 1) return;
          const el = /** @type {HTMLElement} */ (node);
          // Netflix 常见字幕容器
          if (el.classList?.contains("player-timedtext") || el.querySelector?.(".player-timedtext")) {
            el.querySelectorAll?.("span").forEach((s) => translateNode(s));
          }
          // 兜底：任意新增节点下的 span
          el.querySelectorAll?.(".player-timedtext span").forEach((s) => translateNode(s));
        });
      }
    });

    mo.observe(root, { childList: true, subtree: true });
  } catch (err) {
    kissLog(err, "initNetflixBilingual");
  }
}

