import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import { useI18n } from "../../hooks/I18n";
import { useSetting } from "../../hooks/Setting";

export default function VideoSubtitles() {
  const i18n = useI18n();
  const { setting, updateSetting } = useSetting();
  const { videoSubSetting = { enable: true, sites: "" } } = setting || {};
  const { enable, sites } = videoSubSetting;

  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <FormControlLabel
        control={
          <Switch
            checked={enable}
            onChange={(e) =>
              updateSetting({ videoSubSetting: { ...videoSubSetting, enable: e.target.checked } })
            }
          />
        }
        label={i18n("video_subtitle")}
      />
      <TextField
        label={i18n("video_sites" )}
        value={sites}
        onChange={(e) => updateSetting({ videoSubSetting: { ...videoSubSetting, sites: e.target.value } })}
        multiline
        minRows={6}
        helperText={i18n("video_sites_help")}
      />
      <div style={{ fontSize: 12, opacity: 0.75 }}>
        <div>• YouTube: www.youtube.com, m.youtube.com</div>
        <div>• Netflix: www.netflix.com</div>
      </div>
    </Stack>
  );
}

