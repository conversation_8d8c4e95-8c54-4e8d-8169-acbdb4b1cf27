import React from "react";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import MenuItem from "@mui/material/MenuItem";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import { useI18n } from "../../hooks/I18n";
import { useSetting } from "../../hooks/Setting";
import { OPT_EXPORT_MODE_ALL } from "../../config";

export default function ExportSetting() {
  const i18n = useI18n();
  const { setting, updateSetting } = useSetting();
  const { exportSetting = {} } = setting || {};
  const {
    mode = "bilingual",
    keepStyles = true,
    inlineCSS = true,
    includeImages = true,
    addTimestamp = true,
  } = exportSetting;

  const handleChange = (field, value) => {
    updateSetting({
      exportSetting: {
        ...exportSetting,
        [field]: value,
      },
    });
  };

  return (
    <Stack spacing={3} sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        {i18n("export_setting")}
      </Typography>

      <Box>
        <TextField
          select
          fullWidth
          label={i18n("export_mode")}
          value={mode}
          onChange={(e) => handleChange("mode", e.target.value)}
          helperText="Choose whether to export both original and translated text, or translated text only"
        >
          {OPT_EXPORT_MODE_ALL.map((modeOption) => (
            <MenuItem key={modeOption} value={modeOption}>
              {i18n(`export_mode_${modeOption}`)}
            </MenuItem>
          ))}
        </TextField>
      </Box>

      <FormControlLabel
        control={
          <Switch
            checked={keepStyles}
            onChange={(e) => handleChange("keepStyles", e.target.checked)}
          />
        }
        label={i18n("export_keep_styles")}
      />

      <FormControlLabel
        control={
          <Switch
            checked={inlineCSS}
            disabled={!keepStyles}
            onChange={(e) => handleChange("inlineCSS", e.target.checked)}
          />
        }
        label={i18n("export_inline_css")}
      />

      <FormControlLabel
        control={
          <Switch
            checked={includeImages}
            onChange={(e) => handleChange("includeImages", e.target.checked)}
          />
        }
        label={i18n("export_include_images")}
      />

      <FormControlLabel
        control={
          <Switch
            checked={addTimestamp}
            onChange={(e) => handleChange("addTimestamp", e.target.checked)}
          />
        }
        label={i18n("export_add_timestamp")}
      />

      <Box sx={{ mt: 2, p: 2, bgcolor: "action.hover", borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          <strong>Tips:</strong>
          <br />
          • Bilingual mode preserves both original and translated text
          <br />
          • Translated-only mode hides original text for cleaner reading
          <br />
          • Inline CSS helps ensure the exported page looks correct offline
          <br />
          • Use keyboard shortcut Alt+E to quickly export the current page
        </Typography>
      </Box>
    </Stack>
  );
}
