import { useMemo, useRef, useState } from "react";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import MenuItem from "@mui/material/MenuItem";
import LinearProgress from "@mui/material/LinearProgress";
import Alert from "@mui/material/Alert";
import { useI18n } from "../../hooks/I18n";
import { useSetting } from "../../hooks/Setting";
import {
  DEFAULT_TRANS_APIS,
  OPT_LANGS_TO,
  OPT_TRANS_ALL,
} from "../../config";
import { apiTranslate } from "../../apis";

function parseSrt(text) {
  const blocks = text
    .replace(/\r/g, "")
    .trim()
    .split(/\n\n+/)
    .map((b) => b.split("\n"));
  return blocks.map((lines) => {
    const idx = lines[0];
    const time = lines[1];
    const content = lines.slice(2).join("\n");
    return { idx, time, content };
  });
}

function stringifySrt(items, combineBilingual = true) {
  return items
    .map(({ idx, time, content, tr }) => {
      const body = combineBilingual
        ? [content, tr].filter(Boolean).join("\n")
        : tr || content;
      return [idx, time, body].join("\n");
    })
    .join("\n\n");
}

// -------- ASS helpers --------
function parseAss(text) {
  const lines = text.replace(/\r/g, "").split("\n");
  const header = [];
  let inEvents = false;
  let formatFields = [];
  const items = [];
  const findFormatIdx = {};
  let idxSeq = 1;

  function splitAssValues(str, fieldCount) {
    const arr = [];
    let remain = str;
    for (let i = 0; i < fieldCount - 1; i++) {
      const pos = remain.indexOf(',');
      if (pos === -1) {
        arr.push(remain);
        remain = '';
      } else {
        arr.push(remain.slice(0, pos));
        remain = remain.slice(pos + 1);
      }
    }
    arr.push(remain);
    return arr.map((s) => s.trim());
  }

  for (const raw of lines) {
    const line = raw.trim();
    if (!inEvents) {
      header.push(raw);
      if (/^\[Events\]/i.test(line)) {
        inEvents = true;
      }
      continue;
    }
    if (/^Format:/i.test(line)) {
      formatFields = line.substring(7).split(',').map((s) => s.trim());
      formatFields.forEach((f, i) => (findFormatIdx[f.toLowerCase()] = i));
      continue;
    }
    if (/^Dialogue:/i.test(line)) {
      const valuesStr = line.substring(9).trim();
      if (formatFields.length === 0) continue;
      const values = splitAssValues(valuesStr, formatFields.length);
      const textIdx = findFormatIdx['text'];
      const startIdx = findFormatIdx['start'];
      const endIdx = findFormatIdx['end'];
      const content = values[textIdx] || '';
      const start = values[startIdx] || '';
      const end = values[endIdx] || '';
      const time = `${start} --> ${end}`;
      items.push({ idx: String(idxSeq++), time, content, __assValues: values });
    }
  }

  return { header, formatFields, items };
}

function stringifyAss({ header, formatFields, items }, combineBilingual = true) {
  const out = [];
  const lower = formatFields.map((s) => s.toLowerCase());
  const textIdx = lower.indexOf('text');
  out.push(...header);
  // ensure there is a Format line in header already
  // Dialogue lines
  for (const it of items) {
    const values = it.__assValues.slice();
    const orig = it.content || '';
    const tr = it.tr || '';
    const text = combineBilingual ? [orig, tr].filter(Boolean).join('\\N') : (tr || orig);
    values[textIdx] = text;
    out.push(`Dialogue: ${values.join(',')}`);
  }
  return out.join('\n');
}

function protectAssTags(text) {
  const tags = [];
  const safe = text.replace(/\{[^}]*\}/g, (m) => {
    const key = `[[KISS_TAG_${tags.length}]]`;
    tags.push(m);
    return key;
  });
  return {
    text: safe,
    restore: (s) => s.replace(/\[\[KISS_TAG_(\d+)\]\]/g, (_, p) => tags[parseInt(p, 10)] || ''),
  };
}

// -------- VTT helpers --------
function parseVtt(text) {
  const lines = text.replace(/\r/g, "").split("\n");
  const header = [];
  let i = 0;
  if (/^WEBVTT/.test(lines[0])) {
    header.push(lines[0]);
    i = 1;
    // 跳过可能存在的注释或空行
    while (i < lines.length && lines[i].trim() === "") i++;
  }
  const items = [];
  let idx = 1;
  while (i < lines.length) {
    let idLine = "";
    // 可选的编号行
    if (lines[i] && /^\d+$/.test(lines[i].trim())) {
      idLine = lines[i].trim();
      i++;
    }
    if (i >= lines.length) break;
    const timeLine = lines[i++];
    const contentLines = [];
    while (i < lines.length && lines[i].trim() !== "") {
      contentLines.push(lines[i++]);
    }
    while (i < lines.length && lines[i].trim() === "") i++;
    const content = contentLines.join("\n");
    items.push({ idx: idLine || String(idx++), time: timeLine, content });
  }
  return { header, items };
}

function stringifyVtt({ header, items }, combineBilingual = true) {
  const out = [];
  if (header && header.length > 0) {
    out.push(header[0]);
    out.push("");
  } else {
    out.push("WEBVTT");
    out.push("");
  }
  for (const it of items) {
    if (it.idx) out.push(it.idx);
    const body = combineBilingual
      ? [it.content || "", it.tr || ""].filter(Boolean).join("\n")
      : it.tr || it.content || "";
    out.push(it.time);
    out.push(body);
    out.push("");
  }
  return out.join("\n");
}

export default function Subtitles() {
  const i18n = useI18n();
  const { setting } = useSetting();
  const inputRef = useRef();
  const [file, setFile] = useState(null);
  const [items, setItems] = useState([]);
  const [subType, setSubType] = useState("srt"); // srt | ass | vtt
  const [assHeader, setAssHeader] = useState([]);
  const [assFormat, setAssFormat] = useState([]);
  const [translator, setTranslator] = useState(OPT_TRANS_ALL[0]);
  const [toLang, setToLang] = useState("zh-CN");
  const [bilingual, setBilingual] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [progress, setProgress] = useState(0);
  const cancelRef = useRef({ cancel: false });

  const transApis = setting?.transApis || DEFAULT_TRANS_APIS;

  const optApis = useMemo(
    () =>
      OPT_TRANS_ALL.map((key) => ({
        ...(transApis[key] || DEFAULT_TRANS_APIS[key]),
        apiKey: key,
      }))
        .filter((item) => !item.isDisabled)
        .map(({ apiKey, apiName }) => ({ key: apiKey, name: apiName || apiKey })),
    [transApis]
  );

  const handleOpen = () => inputRef.current?.click();

  const handleSelect = async (e) => {
    setError("");
    const f = e.target.files?.[0];
    if (!f) return;
    setFile(f);
    const text = await f.text();
    try {
      if (/\.ass$/i.test(f.name) || /\n\[Events\]/.test(text)) {
        const { header, formatFields, items } = parseAss(text);
        setSubType("ass");
        setAssHeader(header);
        setAssFormat(formatFields);
        setItems(items);
      } else if (/\.vtt$/i.test(f.name) || /^WEBVTT/.test(text)) {
        const { header, items } = parseVtt(text);
        setSubType("vtt");
        setAssHeader(header);
        setItems(items);
      } else {
        const list = parseSrt(text);
        setSubType("srt");
        setItems(list);
      }
    } catch (err) {
      setError("Unsupported subtitle format");
    }
  };

  const handleTranslate = async () => {
    if (items.length === 0) return;
    setLoading(true);
    setError("");
    try {
      const apiSetting = transApis[translator] || DEFAULT_TRANS_APIS[translator];
      const fromLang = "auto";
      const batch = 20;
      for (let i = 0; i < items.length; i += batch) {
        const slice = items.slice(i, i + batch);
        // 串行逐段调用，避免 prompt 拼接导致时序错乱
        for (let j = 0; j < slice.length; j++) {
          const { content } = slice[j];
          let text = content;
          let restore = (s) => s;
          if (subType === "ass") {
            const p = protectAssTags(text);
            text = p.text;
            restore = p.restore;
          }
          if (cancelRef.current.cancel) break;
          const [tr] = await apiTranslate({
            translator,
            text,
            fromLang,
            toLang,
            apiSetting,
            usePool: true,
          });
          slice[j].tr = restore(tr || "");
          setProgress(((i + j + 1) / items.length) * 100);
        }
        setItems((prev) => {
          const next = prev.slice();
          for (let k = 0; k < slice.length; k++) {
            const idx = i + k;
            next[idx] = { ...next[idx], tr: slice[k].tr };
          }
          return next;
        });
        if (cancelRef.current.cancel) break;
      }
    } catch (err) {
      setError(err.message || String(err));
    } finally {
      setLoading(false);
      cancelRef.current.cancel = false;
      setProgress(0);
    }
  };

  const handleDownload = () => {
    if (!file || items.length === 0) return;
    let name;
    let content;
    if (subType === "ass") {
      name = file.name.replace(/\.ass$/i, "") + (bilingual ? ".bilingual.ass" : ".translated.ass");
      content = stringifyAss({ header: assHeader, formatFields: assFormat, items }, bilingual);
    } else if (subType === "vtt") {
      name = file.name.replace(/\.vtt$/i, "") + (bilingual ? ".bilingual.vtt" : ".translated.vtt");
      content = stringifyVtt({ header: assHeader, items }, bilingual);
    } else {
      name = file.name.replace(/\.srt$/i, "") + (bilingual ? ".bilingual.srt" : ".translated.srt");
      content = stringifySrt(items, bilingual);
    }
    const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = name;
    a.click();
    setTimeout(() => URL.revokeObjectURL(url), 30000);
  };

  return (
    <Stack spacing={2} sx={{ p: 2 }}>
      <input ref={inputRef} type="file" accept=".srt,.ass,.vtt" hidden onChange={handleSelect} />
      <Stack direction="row" spacing={2}>
        <Button variant="contained" onClick={handleOpen}>
          {i18n("import_subtitle")}
        </Button>
        <TextField
          select
          size="small"
          value={translator}
          label={i18n("translate_service")}
          onChange={(e) => setTranslator(e.target.value)}
        >
          {optApis.map(({ key, name }) => (
            <MenuItem key={key} value={key}>
              {name}
            </MenuItem>
          ))}
        </TextField>
        <TextField
          select
          size="small"
          value={toLang}
          label={i18n("to_lang")}
          onChange={(e) => setToLang(e.target.value)}
        >
          {OPT_LANGS_TO.map(([lang, name]) => (
            <MenuItem key={lang} value={lang}>
              {name}
            </MenuItem>
          ))}
        </TextField>
        <TextField
          select
          size="small"
          value={bilingual ? "bilingual" : "single"}
          label={i18n("subtitle_mode")}
          onChange={(e) => setBilingual(e.target.value === "bilingual")}
        >
          <MenuItem value="bilingual">{i18n("bilingual")}</MenuItem>
          <MenuItem value="single">{i18n("translation_only")}</MenuItem>
        </TextField>
      </Stack>

      <Stack direction="row" spacing={2}>
        <Button variant="outlined" disabled={items.length === 0 || loading} onClick={handleTranslate}>
          {i18n("translate")}
        </Button>
        {loading && (
          <Button
            variant="outlined"
            color="error"
            onClick={() => {
              cancelRef.current.cancel = true;
            }}
          >
            {i18n("stop")}
          </Button>
        )}
        <Button variant="outlined" disabled={items.length === 0} onClick={handleDownload}>
          {i18n("export_bilingual_subtitle")}
        </Button>
      </Stack>

      {loading && (
        <LinearProgress variant={progress ? "determinate" : "indeterminate"} value={progress} />
      )}
      {error && <Alert severity="error">{error}</Alert>}
      {file && <Alert severity="info">{file.name}</Alert>}
    </Stack>
  );
}
