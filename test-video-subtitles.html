<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Subtitle Test - KISS Translator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .subtitle-container {
            background: #000;
            color: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 4px;
            position: relative;
            min-height: 60px;
        }
        .ytp-caption-segment {
            background: rgba(0,0,0,0.8);
            padding: 4px 8px;
            border-radius: 2px;
            margin: 5px 0;
            display: inline-block;
        }
        .player-timedtext span {
            background: rgba(0,0,0,0.7);
            padding: 2px 6px;
            border-radius: 2px;
            margin: 3px 0;
            display: block;
            text-align: center;
        }
        .test-controls {
            margin: 10px 0;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        button:hover {
            background: #1565c0;
        }
        .info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <h1>Video Subtitle Translation Test</h1>
    <p>This page simulates video subtitle elements from different platforms to test the KISS Translator video subtitle feature.</p>

    <div class="info">
        <strong>Instructions:</strong>
        <ol>
            <li>Install the KISS Translator extension</li>
            <li>Enable video subtitle translation in the extension options</li>
            <li>Add this page's domain to the video sites list</li>
            <li>Click the buttons below to simulate subtitle changes</li>
            <li>Watch for translated subtitles to appear below the original text</li>
        </ol>
    </div>

    <!-- YouTube Style Test -->
    <div class="test-section">
        <h2>YouTube Style Subtitles</h2>
        <div id="ytp-caption-window-container" class="subtitle-container">
            <div class="ytp-caption-segment" id="yt-subtitle">
                Hello, welcome to this video tutorial.
            </div>
        </div>
        <div class="test-controls">
            <button onclick="changeYouTubeSubtitle()">Change YouTube Subtitle</button>
            <button onclick="addYouTubeSubtitle()">Add New Subtitle</button>
        </div>
    </div>

    <!-- Netflix Style Test -->
    <div class="test-section">
        <h2>Netflix Style Subtitles</h2>
        <div class="subtitle-container">
            <div class="player-timedtext" id="netflix-container">
                <span id="netflix-subtitle">This is a Netflix subtitle example.</span>
            </div>
        </div>
        <div class="test-controls">
            <button onclick="changeNetflixSubtitle()">Change Netflix Subtitle</button>
            <button onclick="addNetflixSubtitle()">Add New Subtitle</button>
        </div>
    </div>

    <!-- Generic Video Test -->
    <div class="test-section">
        <h2>Generic Video Subtitles</h2>
        <div class="subtitle-container">
            <div class="video-subtitle" id="generic-container">
                <div class="subtitle-text" id="generic-subtitle">
                    Generic video subtitle for testing purposes.
                </div>
            </div>
        </div>
        <div class="test-controls">
            <button onclick="changeGenericSubtitle()">Change Generic Subtitle</button>
            <button onclick="addGenericSubtitle()">Add New Subtitle</button>
        </div>
    </div>

    <!-- Test Status -->
    <div class="test-section">
        <h2>Test Status</h2>
        <div id="test-status">
            <p>Waiting for subtitle changes...</p>
        </div>
        <button onclick="clearAllSubtitles()">Clear All Subtitles</button>
        <button onclick="runAutoTest()">Run Auto Test</button>
    </div>

    <script>
        const subtitleTexts = [
            "Hello, welcome to this video tutorial.",
            "Today we will learn about web development.",
            "Let's start with the basics of HTML and CSS.",
            "JavaScript is a powerful programming language.",
            "Thank you for watching this tutorial.",
            "Don't forget to subscribe for more content.",
            "See you in the next video!",
            "This is a test subtitle for translation.",
            "The quick brown fox jumps over the lazy dog.",
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        ];

        let currentIndex = 0;

        function getRandomSubtitle() {
            return subtitleTexts[Math.floor(Math.random() * subtitleTexts.length)];
        }

        function changeYouTubeSubtitle() {
            const element = document.getElementById('yt-subtitle');
            element.textContent = getRandomSubtitle();
            updateStatus('Changed YouTube subtitle');
        }

        function addYouTubeSubtitle() {
            const container = document.getElementById('ytp-caption-window-container');
            const newSubtitle = document.createElement('div');
            newSubtitle.className = 'ytp-caption-segment';
            newSubtitle.textContent = getRandomSubtitle();
            container.appendChild(newSubtitle);
            updateStatus('Added new YouTube subtitle');
        }

        function changeNetflixSubtitle() {
            const element = document.getElementById('netflix-subtitle');
            element.textContent = getRandomSubtitle();
            updateStatus('Changed Netflix subtitle');
        }

        function addNetflixSubtitle() {
            const container = document.getElementById('netflix-container');
            const newSubtitle = document.createElement('span');
            newSubtitle.textContent = getRandomSubtitle();
            container.appendChild(newSubtitle);
            updateStatus('Added new Netflix subtitle');
        }

        function changeGenericSubtitle() {
            const element = document.getElementById('generic-subtitle');
            element.textContent = getRandomSubtitle();
            updateStatus('Changed generic subtitle');
        }

        function addGenericSubtitle() {
            const container = document.getElementById('generic-container');
            const newSubtitle = document.createElement('div');
            newSubtitle.className = 'subtitle-text';
            newSubtitle.textContent = getRandomSubtitle();
            container.appendChild(newSubtitle);
            updateStatus('Added new generic subtitle');
        }

        function clearAllSubtitles() {
            // Remove all translated subtitles
            document.querySelectorAll('.kiss-subtitle-translation').forEach(el => el.remove());
            updateStatus('Cleared all translated subtitles');
        }

        function updateStatus(message) {
            const status = document.getElementById('test-status');
            const timestamp = new Date().toLocaleTimeString();
            status.innerHTML = `<p><strong>${timestamp}:</strong> ${message}</p>` + status.innerHTML;
        }

        function runAutoTest() {
            updateStatus('Starting auto test...');
            let step = 0;
            const steps = [
                () => changeYouTubeSubtitle(),
                () => changeNetflixSubtitle(), 
                () => changeGenericSubtitle(),
                () => addYouTubeSubtitle(),
                () => addNetflixSubtitle(),
                () => addGenericSubtitle(),
            ];

            const interval = setInterval(() => {
                if (step < steps.length) {
                    steps[step]();
                    step++;
                } else {
                    clearInterval(interval);
                    updateStatus('Auto test completed');
                }
            }, 2000);
        }

        // Initialize
        updateStatus('Test page loaded. Ready for subtitle translation testing.');
    </script>
</body>
</html>
